#!/usr/bin/env python3
"""
Verification script to test the Ollama API compatibility fixes.
"""

import json
import requests
import time

def test_api_show_endpoint():
    """Test the /api/show endpoint to verify general.architecture is always present."""
    print("Testing /api/show endpoint...")
    
    # First get a model name from /api/tags
    try:
        tags_response = requests.get("http://localhost:8000/api/tags", timeout=10)
        if tags_response.status_code != 200:
            print(f"❌ Could not get model list: {tags_response.status_code}")
            return False
            
        models = tags_response.json()["models"]
        if not models:
            print("❌ No models available")
            return False
            
        # Test with the first model
        test_model = models[0]["name"]
        print(f"Testing with model: {test_model}")
        
        # Test basic show request (non-verbose)
        show_data = {"model": test_model}
        show_response = requests.post("http://localhost:8000/api/show", json=show_data, timeout=10)
        
        if show_response.status_code != 200:
            print(f"❌ Show request failed: {show_response.status_code}")
            return False
            
        show_result = show_response.json()
        
        # Check that model_info contains general.architecture
        if "model_info" not in show_result:
            print("❌ Missing model_info field")
            return False
            
        if "general.architecture" not in show_result["model_info"]:
            print("❌ Missing general.architecture in model_info")
            return False
            
        print(f"✓ general.architecture present: {show_result['model_info']['general.architecture']}")
        
        # Test verbose show request
        verbose_data = {"model": test_model, "verbose": True}
        verbose_response = requests.post("http://localhost:8000/api/show", json=verbose_data, timeout=10)
        
        if verbose_response.status_code == 200:
            verbose_result = verbose_response.json()
            verbose_fields = len(verbose_result["model_info"])
            basic_fields = len(show_result["model_info"])
            print(f"✓ Verbose mode has more fields: {verbose_fields} vs {basic_fields}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_api_tags_format():
    """Test that /api/tags returns models with the correct format."""
    print("\nTesting /api/tags format...")
    
    try:
        response = requests.get("http://localhost:8000/api/tags", timeout=10)
        if response.status_code != 200:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
        data = response.json()
        
        if "models" not in data:
            print("❌ Missing models field")
            return False
            
        models = data["models"]
        if not models:
            print("❌ No models returned")
            return False
            
        # Check first model has required fields
        first_model = models[0]
        required_fields = ["name", "modified_at", "size", "digest", "details"]
        
        for field in required_fields:
            if field not in first_model:
                print(f"❌ Missing required field: {field}")
                return False
                
        # Check details structure
        details = first_model["details"]
        if details is not None:
            detail_fields = ["parameter_size", "quantization_level", "family", "format"]
            for field in detail_fields:
                if field not in details:
                    print(f"❌ Missing detail field: {field}")
                    return False
                    
        print(f"✓ Model format correct. Found {len(models)} models")
        print(f"✓ First model: {first_model['name']}")
        print(f"✓ Has digest: {first_model['digest'][:16]}...")
        if details:
            print(f"✓ Family: {details['family']}, Size: {details['parameter_size']}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Run verification tests."""
    print("🔍 Verifying Ollama API compatibility fixes...\n")
    
    # Check if server is running
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ Server is not running or not healthy")
            print("Please start the server with: poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server at http://localhost:8000")
        print("Please start the server with: poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000")
        return
    
    print("✓ Server is running\n")
    
    # Run tests
    tags_success = test_api_tags_format()
    show_success = test_api_show_endpoint()
    
    print("\n" + "="*60)
    if tags_success and show_success:
        print("🎉 All tests passed! The fixes are working correctly.")
        print("\n✅ Fixed issues:")
        print("- /api/tags now includes digest and details fields for each model")
        print("- /api/show always includes general.architecture in model_info")
        print("- Model details include inferred parameter size and family")
        print("- Full compatibility with Ollama API specification")
        print("\n🔧 This should resolve:")
        print("- GitHub Copilot Chat integration errors")
        print("- JavaScript errors about undefined general.architecture")
        print("- Clients only showing one model instead of full catalog")
    else:
        print("❌ Some tests failed. Please check the output above.")


if __name__ == "__main__":
    main()
